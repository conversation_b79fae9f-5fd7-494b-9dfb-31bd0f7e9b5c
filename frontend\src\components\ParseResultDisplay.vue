<template>
  <el-card class="results-card">
    <template #header>
      <div class="results-header">
        <h3>解析結果</h3>
        <div class="result-stats">
          <el-tag>{{ parseResult?.statistics?.total_pages || 0 }} 頁</el-tag>
          <el-tag>{{ parseResult?.statistics?.total_words || 0 }} 字</el-tag>
          <el-tag>{{ formatFileSize(parseResult?.statistics?.file_size_bytes) }}</el-tag>
          <el-tag>{{ formatTime(parseResult?.statistics?.processing_time) }}</el-tag>
        </div>
      </div>
    </template>

    <el-tabs v-model="activeTab" type="card" class="result-tabs">
      <el-tab-pane label="審查要項表" name="previewTable">
        <PreviewItemTable
          :items="reviewTasks"
          @task-restarted="handleTaskRestarted"
          @task-started="handleTaskStarted"
        />
      </el-tab-pane>
      <!-- 文字內容 -->
      <el-tab-pane label="文字內容" name="text">
        <div class="text-content-container">
          <div class="content-toolbar">
            <el-button size="small" @click="copyText">
              <el-icon><copy-document /></el-icon>
              複製文字
            </el-button>
            <el-button size="small" @click="searchInText">
              <el-icon><search /></el-icon>
              搜索
            </el-button>
            <el-button size="small" @click="downloadText">
              <el-icon><download /></el-icon>
              下載文字
            </el-button>
          </div>
          <div class="text-content">
            <el-input
              :model-value="parseResult?.text_content || ''"
              type="textarea"
              :rows="25"
              readonly
              placeholder="解析出的文字內容將顯示在這裡..."
              class="text-area"
            />
          </div>
        </div>
      </el-tab-pane>

      <!-- 統計信息 -->
      <el-tab-pane label="統計信息" name="stats" v-if="parseResult?.statistics">
        <ResultStatistics
          :statistics="parseResult.statistics"
          :parse-method="parseResult.parse_method"
          :status="parseResult.status"
          :show-detailed="true"
        />
      </el-tab-pane>

      <!-- 頁面詳情 -->
      <el-tab-pane label="頁面詳情" name="pages" v-if="parseResult?.pages && parseResult.pages.length > 0">
        <div class="pages-content">
          <div class="pages-toolbar">
            <span class="pages-info">共 {{ parseResult?.pages?.length || 0 }} 頁</span>
            <el-select v-model="selectedPage" placeholder="選擇頁面" style="width: 150px;">
              <el-option
                v-for="(page, index) in parseResult?.pages || []"
                :key="index"
                :label="`第 ${page.page_number} 頁`"
                :value="index"
              />
            </el-select>
          </div>
          <div class="page-content" v-if="currentPage">
            <el-card class="page-card">
              <template #header>
                <div class="page-header">
                  <h4>第 {{ currentPage.page_number }} 頁</h4>
                  <div class="page-stats">
                    <el-tag size="small">{{ currentPage.word_count || 0 }} 字</el-tag>
                    <el-tag size="small" v-if="currentPage.confidence">
                      信心度: {{ (currentPage.confidence * 100).toFixed(1) }}%
                    </el-tag>
                  </div>
                </div>
              </template>
              <el-input
                v-model="currentPage.text"
                type="textarea"
                :rows="15"
                readonly
                placeholder="此頁面沒有文字內容"
              />
            </el-card>
          </div>
        </div>
      </el-tab-pane>

      <!-- 圖片信息 -->
      <el-tab-pane label="圖片信息" name="images" v-if="parseResult?.images && parseResult.images.length > 0">
        <div class="images-content">
          <div class="images-grid">
            <el-card
              v-for="(image, index) in parseResult?.images || []"
              :key="index"
              class="image-card"
              shadow="hover"
            >
              <template #header>
                <div class="image-header">
                  <span>圖片 {{ index + 1 }}</span>
                  <span class="image-page">第 {{ image.page_number }} 頁</span>
                </div>
              </template>
              <div class="image-info">
                <p v-if="image.description">{{ image.description }}</p>
                <div class="image-details">
                  <el-tag size="small" v-if="image.width && image.height">
                    {{ image.width }} × {{ image.height }}
                  </el-tag>
                  <el-tag size="small" v-if="image.ai_extracted" type="success">
                    AI提取
                  </el-tag>
                </div>
              </div>
            </el-card>
          </div>
        </div>
      </el-tab-pane>

      <!-- 表格信息 -->
      <el-tab-pane label="表格信息" name="tables" v-if="parseResult?.tables && parseResult.tables.length > 0">
        <div class="tables-content">
          <div class="tables-list">
            <el-card
              v-for="(table, index) in parseResult?.tables || []"
              :key="index"
              class="table-card"
              shadow="hover"
            >
              <template #header>
                <div class="table-header">
                  <span>表格 {{ index + 1 }}</span>
                  <div class="table-info">
                    <el-tag size="small">第 {{ table.page_number }} 頁</el-tag>
                    <el-tag size="small" v-if="table.rows && table.columns">
                      {{ table.rows }} × {{ table.columns }}
                    </el-tag>
                    <el-tag size="small" v-if="table.ai_extracted" type="success">
                      AI提取
                    </el-tag>
                  </div>
                </div>
              </template>
              <div class="table-content">
                <p v-if="table.description" class="table-description">{{ table.description }}</p>
                <el-table
                  v-if="table.data && table.data.length > 0"
                  :data="formatTableData(table.data)"
                  border
                  size="small"
                  max-height="300"
                >
                  <el-table-column
                    v-for="(col, colIndex) in getTableColumns(table.data)"
                    :key="colIndex"
                    :prop="`col${colIndex}`"
                    :label="`列 ${colIndex + 1}`"
                    min-width="100"
                  />
                </el-table>
              </div>
            </el-card>
          </div>
        </div>
      </el-tab-pane>

      <!-- 操作 -->
      <el-tab-pane label="操作" name="actions">
        <div class="actions-content">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-card class="action-card">
                <h4>保存到知識庫</h4>
                <p>將解析結果保存到知識庫中，便於後續查詢和管理</p>
                <el-button type="primary" @click="$emit('save-to-knowledge')">
                  <el-icon><folder-add /></el-icon>
                  保存到知識庫
                </el-button>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card class="action-card">
                <h4>開始 GraphRAG 訓練</h4>
                <p>使用此文檔進行知識圖譜訓練，建立智能關聯</p>
                <el-button type="success" @click="$emit('start-training')">
                  <el-icon><setting /></el-icon>
                  開始訓練
                </el-button>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card class="action-card">
                <h4>導出結果</h4>
                <p>將解析結果導出為不同格式的文件</p>
                <el-dropdown @command="handleExport">
                  <el-button>
                    <el-icon><download /></el-icon>
                    導出結果
                    <el-icon class="el-icon--right"><arrow-down /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="txt">導出為 TXT</el-dropdown-item>
                      <el-dropdown-item command="json">導出為 JSON</el-dropdown-item>
                      <el-dropdown-item command="csv">導出為 CSV</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>
    </el-tabs>
  </el-card>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Download,
  FolderAdd,
  Setting,
  ArrowDown,
  CopyDocument,
  Search
} from '@element-plus/icons-vue'
import ResultStatistics from './ResultStatistics.vue'
import PreviewItemTable from './PreviewItemTable.vue'
import { taskAPI } from '@/services/api'

// Props
interface Props {
  parseResult: any
}

const props = defineProps<Props>()

// 調試日誌
console.log('🎨 ParseResultDisplay 組件載入:', {
  hasParseResult: !!props.parseResult,
  parseResultKeys: props.parseResult ? Object.keys(props.parseResult) : [],
  taskId: props.parseResult?.task_id,
  status: props.parseResult?.status,
  hasTextContent: !!props.parseResult?.text_content,
  textContentLength: props.parseResult?.text_content?.length || 0
})

// Emits
const emit = defineEmits<{
  'save-to-knowledge': []
  'start-training': []
  'export': [format: string]
}>()

// 響應式數據
const activeTab = ref('previewTable')
const selectedPage = ref(0)
const reviewTasks = ref<any[]>([])
const loading = ref(false)

// 計算屬性
const currentPage = computed(() => {
  if (props.parseResult?.pages && props.parseResult.pages.length > 0) {
    return props.parseResult.pages[selectedPage.value]
  }
  return null
})

// 獲取購案ID
const purchaseId = computed(() => {
  return props.parseResult?.purchase_id || null
})

// 方法
// 獲取購案審查任務列表
const loadReviewTasks = async () => {
  if (!purchaseId.value) {
    console.log('⚠️ 沒有購案ID，使用默認任務列表')
    return
  }

  loading.value = true
  try {
    console.log('📋 開始載入購案審查任務:', purchaseId.value)

    // 獲取該購案的所有任務
    const response = await taskAPI.getPurchaseTasks(purchaseId.value)

    const tasks = response.data.tasks || []
    console.log('📋 獲取到任務列表:', tasks.length, '個任務')

    // 定義審查要項映射
    const reviewItemMapping: Record<string, { title: string; memo: string }> = {
      'regulation_compliance': { title: '法規比對', memo: '與中心相關作業規定比對' },
      'mainland_product_check': { title: '陸製品限制比對', memo: '與中心相關作業規定比對' },
      'requirement_analysis': { title: '需求合理性(含籌補率)', memo: '生產用料以料件籌補分析表比對審查' },
      'part_number_compliance': { title: '料號合規性', memo: '生產用料籌補是否引用正式料號' },
      'budget_analysis': { title: '預算合理性(歷史購價)', memo: '申購料件歷史單價' },
      'procurement_schedule': { title: '籌補期程合理性', memo: '申購料件籌補依據與協議書簽署期程是否<60天' },
      'inspection_completeness': { title: '檢驗技資完整性', memo: '申購料件驗收檢驗項目表是否已完備' },
      'budget_consistency': { title: '預算單、總價相符', memo: '採購計畫各項單價*數量及加總後總價是否相符' },
      'major_procurement_approval': { title: '巨額及重大採購是否依規定簽報單位主官核准', memo: '是否填報巨額及重大採購預期效益評估報告' },
      'warranty_terms': { title: '保固條款', memo: '保固條款是否引用國防部內購財物採購契約通用條款第13條及國防部工程、財物暨勞務採購投標須知第34點' },
      'penalty_overdue': { title: '罰則:逾期罰款', memo: '每日罰款金額?罰款總額上限?解除或終止契約條件?' },
      'penalty_breach': { title: '罰則:違約罰則', memo: '違約罰則要求' },
      'equivalent_product': { title: '同等品要求', memo: '同等品要求為何?' },
      'after_sales_service': { title: '售後服務與教育訓練', memo: '售後服務要求為何?' },
      'product_specification': { title: '品名料號及規格報價及決標方式', memo: '是否有填註' }
    }

    // 轉換任務數據為審查要項格式
    const mappedTasks = tasks
      .filter((task: any) => reviewItemMapping[task.task_type])
      .map((task: any) => {
        const mapping = reviewItemMapping[task.task_type]
        return {
          task_id: task.task_id,
          title: mapping.title,
          memo: mapping.memo,
          status: task.status,
          result: task.result_data ? `/api/v1/task-management/tasks/${task.task_id}/result` : null
        }
      })

    reviewTasks.value = mappedTasks
    console.log('📋 審查任務映射完成:', mappedTasks.length, '個審查要項')

  } catch (error) {
    console.error('❌ 載入購案審查任務失敗:', error)
    ElMessage.error('載入審查任務失敗')
  } finally {
    loading.value = false
  }
}

// 處理任務重啟
const handleTaskRestarted = async (taskId: string) => {
  console.log('🔄 任務重啟:', taskId)
  // 重新載入任務列表
  await loadReviewTasks()
}

// 處理任務啟動
const handleTaskStarted = async (taskId: string) => {
  console.log('▶️ 任務啟動:', taskId)
  // 重新載入任務列表
  await loadReviewTasks()
}

const formatFileSize = (bytes: number): string => {
  if (!bytes) return '未知'
  if (bytes < 1024) return `${bytes} B`
  if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`
  return `${(bytes / (1024 * 1024)).toFixed(1)} MB`
}

const formatTime = (seconds: number): string => {
  if (!seconds || seconds <= 0) return '未知'

  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)

  if (hours > 0) {
    return `${hours}h${minutes}m`
  } else if (minutes > 0) {
    return `${minutes}m${secs}s`
  } else {
    return `${secs}s`
  }
}

const copyText = async () => {
  if (!props.parseResult?.text_content) {
    ElMessage.warning('沒有可複製的文字內容')
    return
  }

  try {
    await navigator.clipboard.writeText(props.parseResult.text_content)
    ElMessage.success('文字已複製到剪貼板')
  } catch (error) {
    ElMessage.error('複製失敗')
  }
}

const searchInText = () => {
  ElMessage.info('搜索功能開發中...')
}

const downloadText = () => {
  if (!props.parseResult?.text_content) {
    ElMessage.warning('沒有可下載的文字內容')
    return
  }

  const blob = new Blob([props.parseResult.text_content], { type: 'text/plain;charset=utf-8' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `解析結果_${props.parseResult.file_id || 'unknown'}.txt`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)

  ElMessage.success('文字文件下載開始')
}

const formatTableData = (data: string[][]): any[] => {
  return data.map(row => {
    const obj: any = {}
    row.forEach((cell, index) => {
      obj[`col${index}`] = cell
    })
    return obj
  })
}

const getTableColumns = (data: string[][]): number[] => {
  if (!data || data.length === 0) return []
  return Array.from({ length: data[0].length }, (_, i) => i)
}

const handleExport = (format: string) => {
  emit('export', format)
}

// 生命週期鉤子
onMounted(() => {
  console.log('🎨 ParseResultDisplay 組件掛載，開始載入審查任務')
  loadReviewTasks()
})

// 監聽購案ID變化
watch(purchaseId, (newPurchaseId) => {
  if (newPurchaseId) {
    console.log('🔄 購案ID變化，重新載入審查任務:', newPurchaseId)
    loadReviewTasks()
  }
}, { immediate: true })
</script>

<style scoped>
.results-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.results-header h3 {
  margin: 0;
  color: #303133;
}

.result-stats {
  display: flex;
  gap: 10px;
}

.result-tabs {
  margin-top: 20px;
}

.text-content-container {
  padding: 20px 0;
}

.content-toolbar {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.text-content .text-area {
  font-family: 'Courier New', monospace;
  line-height: 1.6;
}

.pages-content {
  padding: 20px 0;
}

.pages-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.pages-info {
  font-weight: 500;
  color: #606266;
}

.page-card {
  margin-top: 15px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-header h4 {
  margin: 0;
}

.page-stats {
  display: flex;
  gap: 10px;
}

.images-content,
.tables-content {
  padding: 20px 0;
}

.images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.image-card,
.table-card {
  height: fit-content;
}

.image-header,
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.image-info {
  text-align: center;
}

.image-details {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 10px;
}

.table-description {
  margin-bottom: 15px;
  color: #606266;
  font-style: italic;
}

.tables-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.table-info {
  display: flex;
  gap: 10px;
}

.actions-content {
  padding: 20px 0;
}

.action-card {
  text-align: center;
  height: 100%;
}

.action-card h4 {
  margin: 0 0 10px 0;
  color: #303133;
}

.action-card p {
  margin: 0 0 20px 0;
  color: #606266;
  font-size: 14px;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .results-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  .result-stats {
    flex-wrap: wrap;
  }

  .content-toolbar {
    flex-direction: column;
  }

  .pages-toolbar {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  .page-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .images-grid {
    grid-template-columns: 1fr;
  }

  .actions-content .el-col {
    margin-bottom: 20px;
  }
}
</style>
